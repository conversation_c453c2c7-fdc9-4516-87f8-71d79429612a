import clsx from "clsx";

// =========================================================

// =========================================================

export default function Topbar({
  bgColor,
  textColor
}) {
  return <div className={clsx({
    "fw-bold fs-15 mb-2": true,
    [bgColor || "bg-primary"]: true,
    [textColor || "text-white"]: true
  })}>
      <div className="container py-2 d-md-flex flex-md-row">
        <div className="d-flex flex-row align-items-center">
          <div className="icon text-white fs-22 mt-1 me-2">
            <i className="uil uil-location-pin-alt" />
          </div>

          <address className="mb-0">Make a difference today - Your support matters</address>
        </div>

        {/* <div className="d-flex flex-row align-items-center me-6 ms-auto">
          <div className="icon text-white fs-22 mt-1 me-2">
            <i className="uil uil-phone-volume" />
          </div>

          //<p className="mb-0">+****************</p>
        </div>

        <div className="d-flex flex-row align-items-center">
          <div className="icon text-white fs-22 mt-1 me-2">
            <i className="uil uil-message" />
          </div>

          <p className="mb-0">
            <a href="mailto:<EMAIL>" className="link-white hover">
              <EMAIL>
            </a>
          </p>
        </div> */}
      </div>
    </div>;
}