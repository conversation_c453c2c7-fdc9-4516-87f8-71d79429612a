"use client";

import { Fragment, useState } from "react";
// GLOBAL CUSTOM COMPONENTS
import Navbar from "components/blocks/navbar/navbar-1";
import { Team2 } from "components/blocks/team";
import { Hero2 } from "components/blocks/hero";
import { About2 } from "components/blocks/about";
import { Footer4 } from "components/blocks/footer";
import { Clients1 } from "components/blocks/clients";
import { Process2 } from "components/blocks/process";
import { Services3 } from "components/blocks/services";
import { Portfolio1 } from "components/blocks/portfolio";
import { Testimonial6 } from "components/blocks/testimonial";
import NextLink from "components/reuseable/links/NextLink";
import Topbar from "components/elements/Topbar";
import authConfig from 'configs/auth';
import axios from 'axios';
// MODAL COMPONENTS
import DonateModal from "components/modals/DonateModal";
import LoginModal from "components/modals/LoginModal";
import RegisterModal from "components/modals/RegisterModal";
export default function Demo2() {
  // Contact form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    message: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [formStatus, setFormStatus] = useState({
    submitted: false,
    success: false,
    message: ''
  });

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when field is edited
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };
  
  // Validate form
  const validateForm = () => {
    let valid = true;
    const newErrors = {};
    
    // Validate first name
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
      valid = false;
    }

    // Last name is optional, no validation needed
    
    // No surname validation needed anymore
    
    // Validate email
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
      valid = false;
    } else if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
      valid = false;
    }
    
    // Validate message
    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
      valid = false;
    }
    
    // Validate phone (now required)
    if (!formData.phone.trim()) {
      newErrors.phone = 'Mobile number is required';
      valid = false;
    } else if (!/^[0-9]{10}$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid 10-digit mobile number';
      valid = false;
    }
    
    setFormErrors(newErrors);
    return valid;
  };
  
  const fetchIpAddress = async () => {
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip;
  } catch (error) {
    console.error('Error fetching IP address:', error);
    return null;
  }
};

 const getUrl = endpoint => {
        return authConfig.baseURL + endpoint;
        };
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      setFormStatus({ submitted: true, success: false, message: 'Sending your message...' });
      
      try {
        const ipAddress = await fetchIpAddress();
        
        // Concatenate first and last name for display
        const fullName = formData.lastName.trim() 
          ? `${formData.firstName} ${formData.lastName}`.trim()
          : formData.firstName.trim();
          
      
        const contactUsRequestDTO = {
          name: fullName,
          email: formData.email,
          contactNumber: formData.phone,
          message: formData.message,
          ipAddress: ipAddress
          
        };
          
        axios({
        method: 'post',
        url: getUrl(authConfig.ContactUsEndpoint),
        data: contactUsRequestDTO
      })
        .then(response => {
          console.log('Form submitted successfully:', response.data);
          
        
        // Reset the form after successful submission
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          message: ''
        });
        
        setFormStatus({
          submitted: true,
          success: true,
          message: 'Thank you! Your message has been sent successfully.'
        });
      })
      } catch (error) {
        console.error('Error submitting form:', error);
        setFormStatus({
          submitted: true,
          success: false,
          message: 'There was an error sending your message. Please try again.'
        });
      }
    }
  };
  return <Fragment>
      {/* ========== topbar section ========== */}
      <Topbar />

      {/* ========== header section ========== */}
      <header className="wrapper bg-light">
        <Navbar  
          logoAlt="Pure Heart Foundation" 
          logoImage="/img/pure-heart-logo-bg-white.png"
          logoStyle={{ maxWidth: '200px' }}
          button={
            <div className="d-flex gap-1">
              <NextLink 
                title="Donate" 
                href="#" 
                data-bs-toggle="modal" 
                data-bs-target="#modal-donate" 
                className="btn btn-xxs btn-primary rounded-pill px-3 py-1" 
                style={{ lineHeight: '2.0', fontSize: '0.65rem' }}
              />
              <button 
                className="btn btn-xxs btn-primary rounded-pill px-3 py-1"
                style={{ lineHeight: '2.0', fontSize: '0.65rem' }}
                onClick={() => window.location.href = "https://app.pheart.in/login"}
              >
                Login
              </button>
              <div className="dropdown">
                <button 
                  className="btn btn-xxs btn-primary rounded-pill px-3 py-1 dropdown-toggle"
                  style={{ lineHeight: '2.0', fontSize: '0.65rem' }}
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  Register
                </button>
                <ul className="dropdown-menu">
                  <li><a className="dropdown-item" href="https://app.pheart.in/register/?role=donor">Donor</a></li>
                  <li><a className="dropdown-item" href="https://app.pheart.in/register/?role=ngo">Tenant</a></li>
                </ul>
              </div>
            </div>
          } 
        />
      </header>

      <main className="content-wrapper">
        <section className="wrapper bg-light">
          <div className="container pt-8 pt-md-14">
            <Hero2 />            

            <Services3 />
            <Process2 />

            {/* ========== impact stories section ========== */}
            <div className="row">
              <div className="col-lg-9 col-xl-8 col-xxl-7 mx-auto text-center">
                <h2 className="fs-15 text-uppercase text-muted mb-3">Your Impact Stories</h2>
                <h3 className="display-4 mb-10">
                  Witness How Your Generosity Creates Lasting Change in Communities
                </h3>
              </div>
            </div>
          </div>

          {/* ========== latest projects carousel section ========== */}
          <Portfolio1 />

          <div className="container">
            {/* ========== who are we section ========== */}
            <About2 />

            {/* ========== our team section ========== */}
            {/* <Team2 /> */}

            {/* ========== testimonial section ========== */}
            {/* <Testimonial6 /> */}
          </div>
        </section>
      </main>

      {/* ========== contact form section ========== */}
      <section className="wrapper bg-light">
        <div className="container pt-14 pb-8 pt-md-16 pb-md-8">
          <div className="row">
            <div className="col-lg-10 offset-lg-1 col-xl-8 offset-xl-2">
              <h2 className="display-4 mb-3 text-center">Contact Us</h2>
              <p className="lead text-center mb-6">Have questions or want to get involved? Reach out to us using the form below.</p>
              {formStatus.submitted && (
                <div className={`alert ${formStatus.success ? 'alert-success' : 'alert-danger'} mb-4`}>
                  {formStatus.message}
                </div>
              )}
              
              <form className="contact-form needs-validation" onSubmit={handleSubmit} noValidate>
                <div className="row g-2">
                  <div className="col-md-6">
                    <div className="form-floating mb-2">
                      <input 
                        id="form_firstName" 
                        type="text" 
                        name="firstName" 
                        className={`form-control form-control-sm ${formErrors.firstName ? 'is-invalid' : ''}`} 
                        placeholder="Jane" 
                        value={formData.firstName}
                        onChange={handleInputChange}
                        required 
                      />
                      <label htmlFor="form_firstName" className="small">First Name *</label>
                      {formErrors.firstName && <div className="invalid-feedback small">{formErrors.firstName}</div>}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating mb-2">
                      <input 
                        id="form_lastName" 
                        type="text" 
                        name="lastName" 
                        className={`form-control form-control-sm ${formErrors.lastName ? 'is-invalid' : ''}`} 
                        placeholder="Doe" 
                        value={formData.lastName}
                        onChange={handleInputChange}
                      />
                      <label htmlFor="form_lastName" className="small">Last Name</label>
                      {formErrors.lastName && <div className="invalid-feedback small">{formErrors.lastName}</div>}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating mb-2">
                      <input 
                        id="form_email" 
                        type="email" 
                        name="email" 
                        className={`form-control form-control-sm ${formErrors.email ? 'is-invalid' : ''}`} 
                        placeholder="<EMAIL>" 
                        value={formData.email}
                        onChange={handleInputChange}
                        required 
                      />
                      <label htmlFor="form_email" className="small">Email *</label>
                      {formErrors.email && <div className="invalid-feedback small">{formErrors.email}</div>}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating mb-2">
                      <input 
                        id="form_phone" 
                        type="tel" 
                        name="phone" 
                        className={`form-control form-control-sm ${formErrors.phone ? 'is-invalid' : ''}`} 
                        placeholder="1234567890" 
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                      />
                      <label htmlFor="form_phone" className="small">Mobile Number *</label>
                      {formErrors.phone && <div className="invalid-feedback small">{formErrors.phone}</div>}
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="form-floating mb-2">
                      <textarea 
                        id="form_message" 
                        name="message" 
                        className={`form-control form-control-sm ${formErrors.message ? 'is-invalid' : ''}`} 
                        placeholder="Your message" 
                        style={{ height: 120 }} 
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                      ></textarea>
                      <label htmlFor="form_message" className="small">Message *</label>
                      {formErrors.message && <div className="invalid-feedback small">{formErrors.message}</div>}
                    </div>
                  </div>
                  <div className="col-12 text-center">
                    {formStatus.submitted && (
                      <div className={`small mb-2 ${formStatus.success ? 'text-success' : 'text-danger'}`}>
                        {formStatus.message}
                      </div>
                    )}
                    <button 
                      type="submit" 
                      className="btn btn-sm btn-primary rounded-pill px-4" 
                      disabled={formStatus.submitted && !formStatus.success}
                    >
                      {formStatus.submitted && !formStatus.success ? 'Sending...' : 'Send Message'}
                    </button>
                    <div className="text-muted small mt-2"><strong>*</strong> These fields are required.</div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* ========== footer section ========== */}
      <Footer4 />
      
      {/* Modal Components */}
      <DonateModal />
      
    </Fragment>;
}
