"use client";

import NextLink from "components/reuseable/links/NextLink";
import SocialLinks from "components/reuseable/SocialLinks";
// CUSTOM DATA
import footerNav from "data/footer";
import { useEffect } from "react";

export default function Footer5() {
  useEffect(() => {
    // Add styles to head
    const style = document.createElement('style');
    style.textContent = `
      @keyframes randomFloat1 {
        0% { transform: translate(0, 0); }
        25% { transform: translate(50px, -30px); }
        50% { transform: translate(-20px, 50px); }
        75% { transform: translate(30px, 20px); }
        100% { transform: translate(0, 0); }
      }
      @keyframes randomFloat2 {
        0% { transform: translate(0, 0); }
        33% { transform: translate(-40px, 30px); }
        66% { transform: translate(20px, -40px); }
        100% { transform: translate(0, 0); }
      }
      @keyframes randomFloat3 {
        0% { transform: translate(0, 0); }
        20% { transform: translate(30px, 40px); }
        40% { transform: translate(-40px, -20px); }
        60% { transform: translate(20px, -50px); }
        80% { transform: translate(-30px, 30px); }
        100% { transform: translate(0, 0); }
      }
      @keyframes randomFloat4 {
        0% { transform: translate(0, 0); }
        30% { transform: translate(-30px, -40px); }
        60% { transform: translate(40px, 20px); }
        100% { transform: translate(0, 0); }
      }
      @keyframes randomFloat5 {
        0% { transform: translate(0, 0); }
        25% { transform: translate(40px, 40px); }
        50% { transform: translate(-30px, -30px); }
        75% { transform: translate(-40px, 40px); }
        100% { transform: translate(0, 0); }
      }
      .hover-primary:hover {
        color: #ff6b35 !important;
        transition: color 0.3s ease;
      }
    `;
    document.head.appendChild(style);

    return () => {
      // Clean up
      document.head.removeChild(style);
    };
  }, []);

  return (
    <footer className="position-relative overflow-hidden" style={{
      backgroundColor: '#0a1e33',
      position: 'relative',
      padding: '60px 0 40px',
      zIndex: 1,
      overflow: 'hidden'
    }}>
      {/* Background Circles */}
      <div className="shape-1 position-absolute" style={{
        width: '150px',
        height: '150px',
        borderRadius: '50%',
        background: 'rgba(255, 107, 53, 0.05)',
        top: '5%',
        left: '5%',
        animation: 'randomFloat1 15s ease-in-out infinite'
      }}></div>
      <div className="shape-2 position-absolute" style={{
        width: '80px',
        height: '80px',
        borderRadius: '50%',
        background: 'rgba(255, 255, 255, 0.04)',
        top: '20%',
        right: '10%',
        animation: 'randomFloat2 12s ease-in-out infinite'
      }}></div>
      <div className="shape-3 position-absolute" style={{
        width: '120px',
        height: '120px',
        borderRadius: '50%',
        background: 'rgba(255, 107, 53, 0.03)',
        bottom: '15%',
        left: '10%',
        animation: 'randomFloat3 18s ease-in-out infinite'
      }}></div>
      <div className="shape-4 position-absolute" style={{
        width: '60px',
        height: '60px',
        borderRadius: '50%',
        background: 'rgba(255, 107, 53, 0.04)',
        top: '40%',
        left: '30%',
        animation: 'randomFloat4 20s ease-in-out infinite'
      }}></div>
      <div className="shape-5 position-absolute" style={{
        width: '100px',
        height: '100px',
        borderRadius: '50%',
        background: 'rgba(255, 255, 255, 0.03)',
        bottom: '30%',
        right: '20%',
        animation: 'randomFloat5 16s ease-in-out infinite'
      }}></div>
      
      <div className="container position-relative" style={{ 
        zIndex: 2,
        paddingBottom: '0'
      }}>
        <div className="row gy-4 gy-lg-0">
          <div className="col-md-4 col-lg-3">
            <div className="widget">
              <img
                className="mb-4"
                src="/img/pure-heart-logo-bg-white.png"
                alt="Pure Heart"
                width="150"
                height="auto"
              />
              <p className="mb-4 text-white-50">
                &copy; 2024 Pure Heart. <br className="d-none d-lg-block" />
                Making a difference together.
              </p>

              <SocialLinks className="nav social social-white" />
            </div>
          </div>

          <div className="col-md-4 col-lg-3">
            <div className="widget">
              <h4 className="widget-title text-white mb-3 fw-bold">Reach Out to Us</h4>
              <address className="pe-xl-15 pe-xxl-17 text-white-50">Your support can change lives. Contact us to learn how you can help.</address>
              <NextLink title="<EMAIL>" href="mailto:<EMAIL>" className="link-primary" />
              <br /> 
              <span className="text-white-50">+1 (555) 123-4567</span>
            </div>
          </div>

          <div className="col-md-4 col-lg-3">
            <div className="widget">
              <h4 className="widget-title text-white mb-3 fw-bold">Learn More</h4>
              <ul className="list-unstyled mb-0">
                {footerNav.map(({
                title,
                url
              }) => <li key={title} className="mb-2">
                    <NextLink title={title} href={url} className="text-white-50 hover-primary" />
                  </li>)}
              </ul>
            </div>
          </div>

          <div className="col-md-12 col-lg-3">
            <div className="widget">
              <h4 className="widget-title text-white mb-3 fw-bold">Our Newsletter</h4>
              <p className="mb-4 text-white-50">
                Subscribe to our newsletter to get our news &amp; deals delivered to you.
              </p>

              <div className="newsletter-wrapper">
                <div id="mc_embed_signup2">
                  <form method="post" target="_blank" className="validate dark-fields" id="mc-embedded-subscribe-form2" name="mc-embedded-subscribe-form" action="https://elemisfreebies.us20.list-manage.com/subscribe/post?u=aa4947f70a475ce162057838d&amp;id=b49ef47a9a">
                    <div id="mc_embed_signup_scroll2">
                      <div className="mc-field-group input-group form-floating">
                        <input type="email" name="EMAIL" id="mce-EMAIL2" placeholder="Email Address" className="required email form-control bg-transparent text-white" style={{borderColor: 'rgba(255,255,255,0.2)'}} />

                        <label htmlFor="mce-EMAIL2" className="text-white-50">Email Address</label>
                        <input 
                          value="Join" 
                          type="submit" 
                          name="subscribe" 
                          id="mc-embedded-subscribe2" 
                          className="btn btn-primary" 
                          style={{
                            fontWeight: 600,
                            letterSpacing: '0.5px',
                            backgroundColor: '#ff6b35',
                            border: 'none',
                            boxShadow: '0 4px 10px rgba(255, 107, 53, 0.3)',
                            borderRadius: '5px'
                          }} 
                        />
                      </div>

                      <div id="mce-responses2" className="clear">
                        <div className="response" id="mce-error-response2" style={{
                        display: "none"
                      }} />
                        <div className="response" id="mce-success-response2" style={{
                        display: "none"
                      }} />
                      </div>

                      <div style={{
                      position: "absolute",
                      left: "-5000px"
                    }} aria-hidden="true">
                        <input type="text" tabIndex={-1} name="b_ddc180777a163e0f9f66ee014_4b1bcfa0bc" />
                      </div>

                      <div className="clear" />
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
    </footer>
  );
}