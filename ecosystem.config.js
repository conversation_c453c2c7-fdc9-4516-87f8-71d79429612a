module.exports = {
    apps: [{
      name: 'pheart-guest-dev',
      script: 'npm',
      args: 'run start:dev',
      watch: true,
      env: {
        NODE_ENV: 'development',
        PORT: 3005 // Specify the development port here
        // You can add more environment variables here if necessary
      }
    }, {
      name: 'pheart-guest-prod',
      script: 'npm',
      args: 'run start:prod',
      // instances: 'max', // for production, you might want to run multiple instances
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3022 // Specify the production port here
        // You can add more environment variables here if necessary
      }
    }]
  };
  